import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/item_autocomplete_textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/add_purchase_bill_item/add_edit_purchase_bill_item_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class AddEditPurchaseBilledItem extends StatelessWidget {
  final String tag = "Purchase Item Add/Edit View";
  final LineItemDetailModel? lineItemModel;
  // final AddEditPurchaseController purchaseController;

  final purchaseItemController = AddEdtPurchaseBillItemController();

  AddEditPurchaseBilledItem({super.key, this.lineItemModel}) {
    if (lineItemModel != null) {
      purchaseItemController.initEdit(lineItemModel!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        // ignore: dead_code
        if (false) {
        } else {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Form(
                  key: purchaseItemController.formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                //===============================================Item
                                Expanded(
                                  flex: 2,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'सामान छन्नुहोस्',
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      ItemAutoCompleteTextFieldWithAdd(
                                          itemID: lineItemModel?.itemId ?? "",
                                          controller: purchaseItemController
                                              .itemNameCtrl,
                                          onChangedFn: (value) {
                                            // state.selectedItem = null;
                                          },
                                          onSuggestionSelectedFn: (item) {
                                            debugPrint(
                                                "pachai chaa ee nazar le tmlai lagxa kei vannaa khojdai thiyaou malai ho khojdaii thee tmlai vandeuu naaa");
                                            purchaseItemController
                                                .itemOnSelectHandler(
                                                    item.itemId,
                                                    unitID: item.baseUnitId,
                                                    item: item);
                                          })
                                    ],
                                  ),
                                ),

                                const SizedBox(
                                  width: 10,
                                ),

                                //===============================================Unit
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'एकाइ',
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      DropdownButtonFormField(
                                        isExpanded: true,
                                        value: purchaseItemController
                                            .billedItem.lineItemUnitId,
                                        decoration: formFieldStyle.copyWith(
                                            hintText: "Unit"),
                                        items: purchaseItemController.unitList
                                            .map((billedItemUnit) {
                                          return DropdownMenuItem(
                                            value: billedItemUnit.unitId,
                                            child: Text(
                                                "${billedItemUnit.unitName} (${billedItemUnit.unitShortName})"),
                                          );
                                        }).toList(),
                                        onChanged: (value) =>
                                            purchaseItemController
                                                .unitOnSelectHandler(value!),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 15,
                            ),

                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                //===============================================Quantity
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'परिमाण',
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "qty",
                                        autocorrect: false,
                                        keyboardType: const TextInputType
                                            .numberWithOptions(decimal: true),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                              RegExp(r'^(\d+)?\.?\d{0,2}'))
                                        ],
                                        textInputAction: TextInputAction.next,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Quantity"),
                                        textAlign: TextAlign.end,
                                        controller:
                                            purchaseItemController.qtyCtrl,
                                        onChanged: (value) =>
                                            purchaseItemController
                                                .qtyOnChangeHandler(value),
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(
                                  width: 10,
                                ),

                                //===============================================Price/Rate
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'मूल्य प्रति एकाइ',
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "rate",
                                        autocorrect: false,
                                        keyboardType: const TextInputType
                                            .numberWithOptions(decimal: true),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                              RegExp(r'^(\d+)?\.?\d{0,2}'))
                                        ],
                                        textInputAction: TextInputAction.done,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Price/Unit"),
                                        textAlign: TextAlign.end,
                                        controller:
                                            purchaseItemController.rateCtrl,
                                        onChanged: (value) {
                                          // purchaseItemController
                                          //         .rateCtrl.selection =
                                          //     TextSelection.fromPosition(
                                          //         TextPosition(
                                          //             offset:
                                          //                 purchaseItemController
                                          //                     .rateCtrl
                                          //                     .text
                                          //                     .length));
                                          purchaseItemController
                                              .rateOnChangeHandler(value);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 15,
                            ),

                            //===============================================Amount
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'जम्मा रकम',
                                  style: labelStyle2,
                                ),
                                const SizedBox(height: 5.0),
                                FormBuilderTextField(
                                  name: "amount",
                                  autocorrect: false,
                                  keyboardType:
                                      const TextInputType.numberWithOptions(
                                          decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(
                                        RegExp(r'^(\d+)?\.?\d{0,2}'))
                                  ],
                                  textInputAction: TextInputAction.done,
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Amount"),
                                  textAlign: TextAlign.end,
                                  controller:
                                      purchaseItemController.grossAmountCtrl,
                                  onChanged: (value) {
                                    purchaseItemController
                                            .grossAmountCtrl.selection =
                                        TextSelection.fromPosition(TextPosition(
                                            offset: purchaseItemController
                                                .grossAmountCtrl.text.length));
                                    purchaseItemController
                                        .amountOnChangeHandler(value);
                                  },
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 15,
                            ),

                            // =============================================Discount
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "छुट (Discount)",
                                  style: labelStyle2,
                                ),
                                const SizedBox(height: 5.0),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    SizedBox(
                                      width: 80,
                                      child: FormBuilderTextField(
                                        name: "txn_discount_percent",
                                        autocorrect: false,
                                        keyboardType: const TextInputType
                                            .numberWithOptions(decimal: true),
                                        textInputAction: TextInputAction.done,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            suffix: const Text("%"),
                                            labelText: "%"),
                                        textAlign: TextAlign.end,
                                        controller: purchaseItemController
                                            .discountPercentageCtrl,
                                        onChanged: (value) {
                                          purchaseItemController
                                                  .discountPercentageCtrl
                                                  .selection =
                                              TextSelection.fromPosition(
                                                  TextPosition(
                                                      offset: purchaseItemController
                                                          .discountPercentageCtrl
                                                          .text
                                                          .length));
                                          purchaseItemController
                                              .discountPercentOnChangeHandler(
                                                  value!);
                                        },
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 20,
                                    ),
                                    Expanded(
                                      child: FormBuilderTextField(
                                        name: "txn_discount_amount",
                                        autocorrect: false,
                                        keyboardType: const TextInputType
                                            .numberWithOptions(decimal: true),
                                        textInputAction: TextInputAction.done,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "छुट रकम (Dis. Amount)"),
                                        textAlign: TextAlign.end,
                                        controller: purchaseItemController
                                            .discountAmountCtrl,
                                        onChanged: (value) {
                                          purchaseItemController
                                                  .discountAmountCtrl
                                                  .selection =
                                              TextSelection.fromPosition(
                                                  TextPosition(
                                                      offset:
                                                          purchaseItemController
                                                              .discountAmountCtrl
                                                              .text
                                                              .length));
                                          purchaseItemController
                                              .onDiscountAmoutChange(value);
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 10,
                            ),

                            //===============================================Net Amount
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'खुद रकम',
                                  style: labelStyle2,
                                ),
                                const SizedBox(
                                  height: 5.0,
                                ),
                                FormBuilderTextField(
                                  name: "amount",
                                  autocorrect: false,
                                  readOnly: true,
                                  keyboardType:
                                      const TextInputType.numberWithOptions(
                                          decimal: true),
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(
                                      RegExp(r'^(\d+)?\.?\d{0,2}'),
                                    ),
                                  ],
                                  textInputAction: TextInputAction.done,
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Net Amount"),
                                  textAlign: TextAlign.end,
                                  controller:
                                      purchaseItemController.netAmountCtrl,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              //=================================================Save button
              BottomSaveCancelButton(
                shadow: false,
                onSaveBtnPressedFn: () {
                  if (purchaseItemController.formKey.currentState!.validate()) {
                    if (purchaseItemController.itemNameCtrl.text.isEmpty) {
                      showToastMessage(context,
                          message: "सामान खाली राख्न मिल्दैन |",
                          alertType: AlertType.Error);
                      return;
                    }

                    if (purchaseItemController.qtyCtrl.text.isEmpty ||
                        0 >=
                            parseDouble(purchaseItemController.qtyCtrl.text)!) {
                      showToastMessage(context,
                          message:
                              "परिमाण (Quantity) खाली वा शून्य राख्न मिल्दैन |",
                          alertType: AlertType.Error);
                      return;
                    }

                    if (purchaseItemController.rateCtrl.text.isEmpty ||
                        0 >=
                            parseDouble(
                                purchaseItemController.rateCtrl.text)!) {
                      showToastMessage(context,
                          message:
                              "मूल्य (Price) खाली वा शून्य राख्न मिल्दैन |",
                          alertType: AlertType.Error);
                      return;
                    }
                    if ((purchaseItemController.billedItem.discountPercent ??
                            0.00) >
                        100) {
                      showToastMessage(context,
                          message:
                              "छुट १००% भन्दा ठूलो हुन सक्दैन | \nDiscount can't be greater than 100%.",
                          alertType: AlertType.Error);
                      return;
                    }
                    purchaseItemController.billedItem.itemId =
                        purchaseItemController.selectedItem.value.itemId;
                    purchaseItemController.billedItem.itemName =
                        strTrim(purchaseItemController.itemNameCtrl.text);

                    purchaseItemController.billedItem.lineItemUnitId =
                        purchaseItemController.selectedUnit.unitId;
                    purchaseItemController.billedItem.lineItemUnitName =
                        purchaseItemController.selectedUnit.unitShortName;

                    purchaseItemController.billedItem.pricePerUnit =
                        parseDouble(purchaseItemController.rateCtrl.text);
                    purchaseItemController.billedItem.quantity =
                        parseDouble(purchaseItemController.qtyCtrl.text);

                    purchaseItemController.billedItem.discountPercent =
                        parseDouble(
                            purchaseItemController.discountPercentageCtrl.text);
                    purchaseItemController.billedItem.discountAmount =
                        parseDouble(
                            purchaseItemController.discountAmountCtrl.text);
                    purchaseItemController.billedItem.grossAmount = parseDouble(
                        purchaseItemController.grossAmountCtrl.text);
                    purchaseItemController.billedItem.totalAmount =
                        parseDouble(purchaseItemController.netAmountCtrl.text);

                    AddEditPurchaseBilledItemResponse
                        _addSaleBilledItemResponse =
                        AddEditPurchaseBilledItemResponse(
                            newFlag: false,
                            billedItem: purchaseItemController.billedItem);
                    // Log.d(
                    //     "received item=> ${purchaseItemController.billedItem.toJson()}");
                    Navigator.pop(context, _addSaleBilledItemResponse);
                  }
                },
              ),
            ],
          );
        }
      },
    );
  }
}
