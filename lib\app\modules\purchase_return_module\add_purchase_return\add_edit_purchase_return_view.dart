import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/ledger_autocomplete%20_%20textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_return_module/add_purchase_return/add_edit_purchase_return_controller.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_return_module/add_purchase_return_bill_item/add_edit_purchase_return_bill_item_screen_view.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_return_module/detail_purchase_return/detail_purchase_return_page.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T f(E e, int i)) {
    var i = 0;
    return this.map((e) => f(e, i++));
  }

  void forEachIndex(void f(E e, int i)) {
    var i = 0;
    this.forEach((e) => f(e, i++));
  }
}

class AddEditPurchaseReturnPage extends StatelessWidget {
  final String tag = "Purchase Return Add/Edit Page";

  final String? purchaseReturnID;
  final bool? reaOnlyFlag;

  final purchaseReturnController = AddEditPurchaseReturnController();

  AddEditPurchaseReturnPage({this.purchaseReturnID, this.reaOnlyFlag}) {
    purchaseReturnController.onInit();

    if (null != purchaseReturnID) {
      // initiate edit functonality
      purchaseReturnController.initEdit(
          purchaseReturnID, this.reaOnlyFlag ?? false);
    } else {
      purchaseReturnController.initialize();
    }
  }

  onSave(BuildContext context, {bool forNew = false}) async {
    FocusScope.of(context).unfocus();
    if (purchaseReturnController.formKey.currentState!.validate()) {
      // should check for bill duplicate
      // if (purchaseReturnController
      //     .billNoCtrl.text.isEmpty) {
      //   showToastMessage(context,
      //       message: "बिल न. खाली राख्न मिल्दैन |",
      //       alertType: AlertType.Error);
      //   return;
      // }

      if (purchaseReturnController.transaction.value.txnDateBS!.isEmpty) {
        showToastMessage(context,
            message: "मिति खाली राख्न मिल्दैन |\nPlease fill the date",
            alertType: AlertType.Error);
        return;
      }
      if (null == purchaseReturnController.transaction.value.ledgerId) {
        showToastMessage(context,
            message:
                "आपूर्तिकर्ता खाली राख्न मिल्दैन |\nSupplier name cannnot be empty.",
            alertType: AlertType.Error);
        return;
      }

      if (100 <
              parseDouble(
                  purchaseReturnController.discountPercentageCtrl.text)! ||
          parseDouble(purchaseReturnController.subTotalAmountCtrl.text)! <
              parseDouble(purchaseReturnController.discountAmountCtrl.text)!) {
        showToastMessage(context,
            message:
                "छुट १००% भन्दा ठूलो हुन सक्दैन | \nDiscount can't be greater than 100%.",
            alertType: AlertType.Error);
        return;
      }

      if (purchaseReturnController.totalAmountCtrl.text.isEmpty) {
        showToastMessage(context,
            message: "कुल रकम खाली हुन सक्दैन | \nTotal amount can't be empty.",
            alertType: AlertType.Error);
        return;
      }

      if (0.0 > parseDouble(purchaseReturnController.totalAmountCtrl.text)!) {
        showToastMessage(context,
            message:
                "कुल रकम नकारात्मक हुन सक्दैन | \nTotal amount can't be negative.",
            alertType: AlertType.Error);
        return;
      }

      print("=============>");
      print("=============>");
      print("=============>");
      print("${purchaseReturnController.transaction.value.txnCashAmount!}");
      print("=============>");
      print("=============>");

      if (purchaseReturnController.transaction.value.txnCashAmount! >
          purchaseReturnController.transaction.value.txnTotalAmount!) {
        showToastMessage(context,
            message:
                "भुक्तानी गरिएको रकम बिल रकम भन्दा ठूलो हुन सक्दैन | \nPaid amount can't be greater than bill amount.",
            alertType: AlertType.Error);
        return;
      }

      if (purchaseReturnController.transaction.value.txnPaymentTypeId !=
              PAYMENT_MODE_CASH_ID &&
          !(purchaseReturnController.transaction.value.txnCashAmount! > 0)) {
        showToastMessage(context,
            message:
                "चेक वा बैंक मा प्राप्त रकम 0 राख्न मिल्दैन | \nReceived amount cannot be 0 for Cheque and Bank",
            alertType: AlertType.Error);
        return;
      }

      if (purchaseReturnController.transaction.value.txnPaymentTypeId ==
              PAYMENT_MODE_CHEQUE_ID &&
          (null ==
                  purchaseReturnController
                      .transaction.value.chequeIssueDateBS ||
              "" ==
                  purchaseReturnController
                      .transaction.value.chequeIssueDateBS)) {
        showToastMessage(context,
            message: "चेक खाली राख्न मिल्दैन | \nPlease fill the cheque date",
            alertType: AlertType.Error);
        return;
      }

      ProgressDialog progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      progressDialog.update(message: "Saving data. Please wait....");
      await progressDialog.show();

      bool isLargeFile = await purchaseReturnController
          .checkLargeImage(purchaseReturnController.files);
      if (isLargeFile) {
        await progressDialog.hide();
        showToastMessage(context,
            message: MAX_IMAGE_SIZE_MESSAGE, alertType: AlertType.Error);
        return;
      }

      bool status = false;
      String? txnID;
      try {
        if (!purchaseReturnController.editFlag) {
          txnID = await purchaseReturnController.createPurchaseReturn();
          status = (null != txnID);
        } else {
          status = await purchaseReturnController.updatePurchaseReturn();
        }
      } catch (e) {
        // Log.e(tag, e.toString() + trace.toString());
      }
      await progressDialog.hide();

      if (status) {
        // Navigator.pop(context, true);
        String message = (purchaseReturnController.editFlag)
            ? "Purchase Return Updated Successfully."
            : "Purchase Return Created Successfully.";
        if (forNew) {
          Navigator.of(context).pushReplacementNamed('/addPurchaseReturn');
        } else {
          if (purchaseReturnController.editFlag) {
            Navigator.of(context).pop();
            Navigator.of(context).pushReplacementNamed('/detailPurchaseReturn',
                arguments: DetailPurchaseReturnPage(
                  purchaseReturnID: this.purchaseReturnID,
                ));
          } else {
            Navigator.of(context).pop();
            if (null != txnID) {
              TransactionHelper.goToPrintPage(
                  context, txnID, TxnType.purchaseReturn);
            }
          }
        }

        TransactionHelper.refreshPreviousPages();
        showToastMessage(context, message: message, duration: 2);
      } else {
        showToastMessage(context,
            alertType: AlertType.Error,
            message: "Failed to process operation",
            duration: 2);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool iscashPurchaseReturn =
          purchaseReturnController.transaction.value.ledgerId ==
              CASH_PURCHASE_LEDGER_ID;
      bool hasSubTotal =
          (purchaseReturnController.transaction.value.txnSubTotalAmount !=
                  null &&
              (purchaseReturnController.transaction.value.txnSubTotalAmount ??
                      0.00) >
                  0.0);

      if (purchaseReturnController.isLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
                resizeToAvoidBottomInset: true,
                appBar: AppBar(
                  toolbarHeight: 60,
                  elevation: 4,
                  leading: BackButton(
                    onPressed: () => Navigator.pop(context, false),
                  ),
                  centerTitle: false,
                  backgroundColor: colorPrimary,
                  titleSpacing: -5.0,
                  title: Text(
                    "खरीद फिर्ता\n(Purchase Return)",
                    style: TextStyle(
                        fontSize: 17,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
                ),

                //===========================================================================Body Part
                body: Center(
                  child: Container(
                    color: backgroundColorShade,
                    child: GestureDetector(
                      onTap: () => FocusScope.of(context).unfocus(),
                      child: Container(
                        child: Form(
                          key: purchaseReturnController.formKey,
                          child: SingleChildScrollView(
                            child: Container(
                              child: Column(
                                children: [
                                  Card(
                                    elevation: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 15),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          //===========================Bill No.
                                          Expanded(
                                            flex: 1,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "फिर्ता बिल न. ",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                  name: "bill_no",
                                                  readOnly:
                                                      purchaseReturnController
                                                          .readOnlyFlag,
                                                  autocorrect: false,
                                                  keyboardType:
                                                      TextInputType.text,
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  textAlign: TextAlign.right,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Return Bill No."),
                                                  controller:
                                                      purchaseReturnController
                                                          .billNoCtrl,
                                                  onChanged: (value) {
                                                    purchaseReturnController
                                                            .transaction
                                                            .value
                                                            .txnRefNumberChar =
                                                        value;
                                                    purchaseReturnController
                                                        .transaction
                                                        .refresh();
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),

                                          SizedBox(
                                            width: 20,
                                          ),

                                          //===========================Transaction Date
                                          Expanded(
                                            flex: 1,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "मिति",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                CustomDatePickerTextField(
                                                  readOnly:
                                                      purchaseReturnController
                                                          .readOnlyFlag,
                                                  maxBSDate:
                                                      NepaliDateTime.now(),
                                                  initialValue:
                                                      purchaseReturnController
                                                          .transaction
                                                          .value
                                                          .txnDateBS,
                                                  onChange: (selectedDate) {
                                                    purchaseReturnController
                                                            .transaction
                                                            .value
                                                            .txnDateBS =
                                                        selectedDate;
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  Card(
                                    elevation: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 10),
                                      child: Column(
                                        children: [
                                          //===============================================Party Balance
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Row(
                                                children: [
                                                  Container(
                                                    width: 20,
                                                    height: 20,
                                                    child: Checkbox(
                                                      activeColor: colorPrimary,
                                                      checkColor: Colors.white,
                                                      value: purchaseReturnController
                                                          .iscashPurchaseReturnSelected,
                                                      onChanged:
                                                          purchaseReturnController
                                                                  .readOnlyFlag
                                                              ? null
                                                              : (value) {
                                                                  purchaseReturnController
                                                                          .setiscashPurchaseReturnSelected =
                                                                      value!;
                                                                  if (value) {
                                                                    purchaseReturnController.onChangeParty(LedgerDetailModel(
                                                                        ledgerId:
                                                                            CASH_PURCHASE_LEDGER_ID,
                                                                        ledgerTitle:
                                                                            CASH_PURCHASE_LEDGER_NAME +
                                                                                " Return"));
                                                                    purchaseReturnController
                                                                        .onToggleVat(
                                                                            false);
                                                                  } else {
                                                                    purchaseReturnController.onChangeParty(LedgerDetailModel(
                                                                        ledgerId:
                                                                            null));
                                                                  }
                                                                },
                                                    ),
                                                  ),
                                                  Text(
                                                      "  खुद्रा खरीद फिर्ता\n  (Cash Purchase Return)",
                                                      style: labelStyle2)
                                                ],
                                              ),
                                              RichText(
                                                textAlign: TextAlign.right,
                                                text: TextSpan(
                                                    text: "पुरानो बाँकी: ",
                                                    style: TextStyle(
                                                        color: textColor),
                                                    children: [
                                                      if (null !=
                                                          purchaseReturnController
                                                              .transaction
                                                              .value
                                                              .ledgerId) ...{
                                                        TextSpan(
                                                          text:
                                                              "${purchaseReturnController.selectedLedger.value.balanceAmount ?? 0}",
                                                          style: TextStyle(
                                                              color: ((purchaseReturnController
                                                                              .selectedLedger
                                                                              .value
                                                                              .balanceAmount ??
                                                                          0) >=
                                                                      0.0)
                                                                  ? colorGreenDark
                                                                  : colorRedLight),
                                                        )
                                                      }
                                                    ]),
                                              )
                                            ],
                                          ),

                                          Container(
                                            height: 10,
                                          ),
                                          //===============================================Party Field
                                          if (!purchaseReturnController
                                              .iscashPurchaseReturnSelected)
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'आपूर्तिकर्ताको नाम',
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                LedgerAutoCompleteTextFieldWithAdd(
                                                    excludedIDS: [
                                                      CASH_PURCHASE_LEDGER_ID
                                                    ],
                                                    enableFlag:
                                                        !purchaseReturnController
                                                            .readOnlyFlag,
                                                    labelText: "Supplier Name",
                                                    controller:
                                                        purchaseReturnController
                                                            .partyNameCtrl,
                                                    onChangedFn: (value) {
                                                      // Log.d("called i text change");
                                                    },
                                                    ledgetID:
                                                        purchaseReturnController
                                                            .transaction
                                                            .value
                                                            .ledgerId,
                                                    onSuggestionSelectedFn:
                                                        (LedgerDetailModel
                                                            ledger) {
                                                      purchaseReturnController
                                                          .onChangeParty(
                                                              ledger);
                                                    })
                                              ],
                                            ),
                                          ...iscashPurchaseReturn
                                              ? [
                                                  const SizedBox(
                                                    height: 10,
                                                  ),
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        "खुद्रा आपूर्तिकर्ताको नाम",
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                        name: "display_text",
                                                        // readOnly:
                                                        //     (null == state.selectedLedger.ledgerId)
                                                        //         ? false
                                                        //         : true,
                                                        readOnly:
                                                            purchaseReturnController
                                                                .readOnlyFlag,
                                                        autocorrect: false,
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Billing Name"),
                                                        controller:
                                                            purchaseReturnController
                                                                .displayTextCtrl,
                                                        onChanged: (value) {
                                                          purchaseReturnController
                                                                  .transaction
                                                                  .value
                                                                  .txnDisplayName =
                                                              value;
                                                          purchaseReturnController
                                                              .transaction
                                                              .refresh();
                                                        },
                                                      ),
                                                    ],
                                                  )
                                                ]
                                              : [],

                                          const SizedBox(
                                            height: 10,
                                          ),
                                          if (!iscashPurchaseReturn)
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                //===================================Mobile
                                                Container(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.3,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'फोन नम्बर',
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                          name: "mobile",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          keyboardType:
                                                              TextInputType
                                                                  .number,
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          inputFormatters: [
                                                            FilteringTextInputFormatter
                                                                .digitsOnly
                                                          ],
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle.copyWith(
                                                              labelText:
                                                                  "Contact no.",
                                                              hintText:
                                                                  "Contact no"),
                                                          controller:
                                                              purchaseReturnController
                                                                  .mobileCtrl),
                                                    ],
                                                  ),
                                                ),

                                                //===================================Address
                                                Container(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.5,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'ठेगाना',
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                        name: "address",
                                                        readOnly: true,
                                                        autocorrect: false,
                                                        keyboardType:
                                                            TextInputType.text,
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Address"),
                                                        controller:
                                                            purchaseReturnController
                                                                .addressCtrl,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          if (!iscashPurchaseReturn)
                                            const SizedBox(height: 10.0),

                                          //=====================================PAN No Field
                                          if (!iscashPurchaseReturn)
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "पान / मु. अ. कर नम्बर",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                    name: "pan_no",
                                                    readOnly:
                                                        !purchaseReturnController
                                                            .editFlag,
                                                    autocorrect: false,
                                                    keyboardType:
                                                        TextInputType.number,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .digitsOnly
                                                    ],
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "PAN/VAT No."),
                                                    controller:
                                                        purchaseReturnController
                                                            .panNoCtrl),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  //================================================Item Container
                                  Card(
                                    child: Container(
                                        width: double.infinity,
                                        padding: EdgeInsets.all(0.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: double.infinity,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 10, vertical: 8),
                                              decoration: BoxDecoration(
                                                  color: colorPrimaryLight,
                                                  borderRadius:
                                                      BorderRadius.only(
                                                          topLeft:
                                                              Radius.circular(
                                                                  4),
                                                          topRight:
                                                              Radius.circular(
                                                                  4))),
                                              child: DefaultTextStyle(
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 15,
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Text(
                                                      "खरीद फिर्ता सामानहरु (Return Items)",
                                                    ),
                                                    Text(
                                                      "Total Item: ${purchaseReturnController.items.length}",
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Container(
                                              constraints: BoxConstraints(
                                                  maxHeight: 300),
                                              child: getItemListView(
                                                  context,
                                                  purchaseReturnController
                                                      .items,
                                                  purchaseReturnController),
                                            ),
                                            Container(
                                              margin: EdgeInsets.symmetric(
                                                  vertical: 10),
                                              child: Center(
                                                child: ElevatedButton(
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    backgroundColor:
                                                        colorPrimary,
                                                    foregroundColor:
                                                        colorPrimaryLightest,
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              20.0),
                                                    ),
                                                  ),
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            10.0),
                                                    child: Text(
                                                      "खरीद फिर्ता सामान थप्नुहोस् \n(Add Purchase Return Item)",
                                                      textAlign:
                                                          TextAlign.center,
                                                      style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 15),
                                                    ),
                                                  ),
                                                  onPressed:
                                                      purchaseReturnController
                                                              .readOnlyFlag
                                                          ? null
                                                          : () async {
                                                              var returnedData =
                                                                  await showDialog(
                                                                      context:
                                                                          context,
                                                                      useRootNavigator:
                                                                          true,
                                                                      barrierDismissible:
                                                                          false,
                                                                      builder:
                                                                          (d_c) {
                                                                        return AlertDialog(
                                                                          insetPadding: EdgeInsets.symmetric(
                                                                              horizontal: 10,
                                                                              vertical: 10),
                                                                          contentPadding:
                                                                              EdgeInsets.zero,
                                                                          clipBehavior:
                                                                              Clip.hardEdge,
                                                                          content: Container(
                                                                              width: MediaQuery.of(context).size.width - 20,
                                                                              child: AddEditPurchaseReturnBilledItemScreenView()),
                                                                        );
                                                                      });
                                                              if (null !=
                                                                  returnedData) {
                                                                if (null !=
                                                                    returnedData
                                                                        .billedItem) {
                                                                  purchaseReturnController
                                                                      .items
                                                                      .add(returnedData
                                                                          .billedItem);
                                                                  purchaseReturnController
                                                                      .items
                                                                      .refresh();
                                                                  purchaseReturnController
                                                                      .recalculateForItems();
                                                                  // Log.d(
                                                                  //     "got  billed item ${returnedData.billedItem.toJson()}");
                                                                }
                                                              }
                                                            },
                                                ),
                                              ),
                                            ),
                                          ],
                                        )),
                                  ),

                                  //===============================================Total Amount
                                  Card(
                                    elevation: 2,
                                    child: Column(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 6, horizontal: 8),
                                          decoration: BoxDecoration(
                                              color: colorPrimaryLight,
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(4),
                                                  topRight:
                                                      Radius.circular(4))),
                                          child: Center(
                                              child: Text(
                                            "Bill Totals (जम्मा बिल)",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 16),
                                          )),
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 10,
                                          ),
                                          child: Column(
                                            children: [
                                              // =============================================Sub Total
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "उप कुल",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "txn_subtotal",
                                                    readOnly:
                                                        purchaseReturnController
                                                                .readOnlyFlag
                                                            ? true
                                                            : (0 <
                                                                    purchaseReturnController
                                                                        .items
                                                                        .length)
                                                                ? true
                                                                : false,
                                                    autocorrect: false,
                                                    keyboardType: TextInputType
                                                        .numberWithOptions(
                                                            decimal: true),
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .allow(RegExp(
                                                              r'^(\d+)?\.?\d{0,2}'))
                                                    ],
                                                    maxLength: 10,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            hintText:
                                                                "Sub Total",
                                                            counterText: ''),
                                                    textAlign: TextAlign.end,
                                                    controller:
                                                        purchaseReturnController
                                                            .subTotalAmountCtrl,
                                                    onChanged: (value) {
                                                      if (value != null ||
                                                          (value != null &&
                                                              value
                                                                  .isNotEmpty)) {
                                                        purchaseReturnController
                                                                .subTotalAmountCtrl
                                                                .selection =
                                                            TextSelection.fromPosition(
                                                                TextPosition(
                                                                    offset: purchaseReturnController
                                                                        .subTotalAmountCtrl
                                                                        .text
                                                                        .length));
                                                        purchaseReturnController
                                                            .onSubTotalIndividualChange(
                                                                value,
                                                                editorTag:
                                                                    'txn_subtotal');
                                                      }
                                                      // purchaseReturnController
                                                      //     .onSubTotalIndividualChange(
                                                      //         value ?? "0.00",
                                                      //         editorTag:
                                                      //             'txn_subtotal');
                                                      // purchaseReturnController
                                                      //     .transaction
                                                      //     .refresh();
                                                    },
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 20,
                                              ),

                                              // =============================================Discount
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "छुट (Discount)",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(height: 5.0),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Container(
                                                        width: 80,
                                                        child:
                                                            FormBuilderTextField(
                                                          name:
                                                              "txn_discount_percent",
                                                          readOnly:
                                                              purchaseReturnController
                                                                      .readOnlyFlag ||
                                                                  (!hasSubTotal),
                                                          autocorrect: false,
                                                          keyboardType:
                                                              TextInputType
                                                                  .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration:
                                                              formFieldStyle
                                                                  .copyWith(
                                                                      suffix: Text(
                                                                          "%"),
                                                                      labelText:
                                                                          "%"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              purchaseReturnController
                                                                  .discountPercentageCtrl,
                                                          onChanged: (value) {
                                                            if (value!
                                                                .isEmpty) {
                                                              //remove keyboard
                                                              FocusScope.of(
                                                                      context)
                                                                  .unfocus();
                                                            }
                                                            purchaseReturnController
                                                                    .discountPercentageCtrl
                                                                    .selection =
                                                                TextSelection.fromPosition(TextPosition(
                                                                    offset: purchaseReturnController
                                                                        .discountPercentageCtrl
                                                                        .text
                                                                        .length));
                                                            purchaseReturnController
                                                                .updateDiscountPercentage(
                                                                    value ??
                                                                        "0.00",
                                                                    editorTag:
                                                                        'txn_discount_percent');
                                                          },
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 20,
                                                      ),
                                                      Expanded(
                                                        child: Container(
                                                          child:
                                                              FormBuilderTextField(
                                                            name:
                                                                "txn_discount_amount",
                                                            readOnly: purchaseReturnController
                                                                    .readOnlyFlag ||
                                                                (!hasSubTotal),
                                                            autocorrect: false,
                                                            keyboardType:
                                                                TextInputType
                                                                    .numberWithOptions(
                                                                        decimal:
                                                                            true),
                                                            textInputAction:
                                                                TextInputAction
                                                                    .done,
                                                            style:
                                                                formFieldTextStyle,
                                                            decoration: formFieldStyle
                                                                .copyWith(
                                                                    labelText:
                                                                        "छुट रकम (Dis. Amount)"),
                                                            textAlign:
                                                                TextAlign.end,
                                                            controller:
                                                                purchaseReturnController
                                                                    .discountAmountCtrl,
                                                            onChanged: (value) {
                                                              purchaseReturnController
                                                                      .discountAmountCtrl
                                                                      .selection =
                                                                  TextSelection.fromPosition(TextPosition(
                                                                      offset: purchaseReturnController
                                                                          .discountAmountCtrl
                                                                          .text
                                                                          .length));
                                                              if (value !=
                                                                      null ||
                                                                  (value !=
                                                                          null &&
                                                                      value
                                                                          .isNotEmpty)) {
                                                                purchaseReturnController
                                                                    .updateDiscountAmount(
                                                                        value,
                                                                        editorTag:
                                                                            "txn_discount_amount");
                                                              }
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 25,
                                              ),

                                              //====================================================VAT
                                              // ...iscashPurchaseReturn
                                              //     ? []
                                              //     :
                                              ...[
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Container(
                                                          width: 20,
                                                          height: 20,
                                                          child: Checkbox(
                                                            activeColor:
                                                                colorPrimary,
                                                            checkColor:
                                                                Colors.white,
                                                            value:
                                                                purchaseReturnController
                                                                    .isVatEnabled,
                                                            onChanged: (purchaseReturnController
                                                                        .readOnlyFlag ||
                                                                    (!hasSubTotal))
                                                                ? null
                                                                : (value) {
                                                                    purchaseReturnController
                                                                        .onToggleVat(
                                                                            value!);
                                                                  },
                                                          ),
                                                        ),
                                                        Text("  मु.अ. कर (VAT)",
                                                            style: labelStyle2)
                                                      ],
                                                    ),
                                                    SizedBox(height: 10.0),
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Container(
                                                          width: 80,
                                                          child:
                                                              FormBuilderTextField(
                                                            name:
                                                                "txn_tax_percent",
                                                            readOnly: true,
                                                            autocorrect: false,
                                                            keyboardType:
                                                                TextInputType
                                                                    .numberWithOptions(
                                                                        decimal:
                                                                            true),
                                                            textInputAction:
                                                                TextInputAction
                                                                    .done,
                                                            style:
                                                                formFieldTextStyle,
                                                            decoration: formFieldStyle
                                                                .copyWith(
                                                                    suffix: Text(
                                                                        "%"),
                                                                    labelText:
                                                                        "%"),
                                                            textAlign:
                                                                TextAlign.end,
                                                            controller:
                                                                purchaseReturnController
                                                                    .vatPercentCtrl,
                                                            onChanged: (value) {
                                                              // purchaseReturnController
                                                              // .onvatPercentChange(
                                                              //     value ??
                                                              //         "0.00",
                                                              //     editorTag:
                                                              //         'txn_tax_percent');
                                                            },
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          width: 20,
                                                        ),
                                                        Expanded(
                                                          child: Container(
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_amount",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType: TextInputType
                                                                  .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "मु.अ. कर रकम (VAT Amount) "),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  purchaseReturnController
                                                                      .vatAmountCtrl,
                                                              inputFormatters: [
                                                                FilteringTextInputFormatter
                                                                    .allow(RegExp(
                                                                        r'^(\d+)?\.?\d{0,2}'))
                                                              ],
                                                              onChanged:
                                                                  (value) {
                                                                purchaseReturnController
                                                                    .onvatAmountChange(
                                                                        value ??
                                                                            "0.00",
                                                                        editorTag:
                                                                            'txn_tax_amount');
                                                              },
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 5,
                                                ),
                                              ],

                                              Divider(
                                                height: 5,
                                              ),
                                              Divider(
                                                height: 0,
                                              ),
                                              SizedBox(
                                                height: 15,
                                              ),

                                              // =============================================Total Amount
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "कुल रकम",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(
                                                    height: 5,
                                                  ),
                                                  Container(
                                                      child: FormBuilderTextField(
                                                          name: "txn_total",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          inputFormatters: [
                                                            FilteringTextInputFormatter
                                                                .allow(RegExp(
                                                                    r'^(\d+)?\.?\d{0,2}'))
                                                          ],
                                                          keyboardType:
                                                              TextInputType.numberWithOptions(
                                                                  decimal:
                                                                      true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Total Amount"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              purchaseReturnController
                                                                  .totalAmountCtrl)),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 25,
                                              ),

                                              // =============================================Received Amount
                                              Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.end,
                                                  children: [
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Row(
                                                            children: [
                                                              Container(
                                                                width: 20,
                                                                height: 20,
                                                                child: Checkbox(
                                                                  activeColor:
                                                                      colorPrimary,
                                                                  checkColor:
                                                                      Colors
                                                                          .white,
                                                                  value: iscashPurchaseReturn
                                                                      ? true
                                                                      : (purchaseReturnController
                                                                          .isReceived),
                                                                  onChanged: (purchaseReturnController
                                                                              .readOnlyFlag ||
                                                                          iscashPurchaseReturn ||
                                                                          (!hasSubTotal))
                                                                      ? null
                                                                      : (value) {
                                                                          purchaseReturnController.setIsReceived =
                                                                              value!;
                                                                          if (value) {
                                                                            purchaseReturnController.transaction.value.txnCashAmount =
                                                                                (purchaseReturnController.transaction.value.txnTotalAmount ?? 0.0);
                                                                            purchaseReturnController.transaction.value.txnBalanceAmount =
                                                                                0.00;
                                                                            purchaseReturnController.receivedAmountCtrl.text =
                                                                                (purchaseReturnController.transaction.value.txnTotalAmount ?? 0.00).toStringAsFixed(2);

                                                                            purchaseReturnController.dueAmountCtrl.text =
                                                                                "0.00";
                                                                          } else {
                                                                            purchaseReturnController.receivedAmountCtrl.text =
                                                                                "0.00";
                                                                            purchaseReturnController.transaction.value.txnCashAmount =
                                                                                0.00;
                                                                            purchaseReturnController.transaction.value.txnBalanceAmount =
                                                                                purchaseReturnController.transaction.value.txnTotalAmount ?? 0.0;
                                                                            purchaseReturnController.dueAmountCtrl.text =
                                                                                (purchaseReturnController.transaction.value.txnTotalAmount ?? 0.00).toStringAsFixed(2);
                                                                          }
                                                                        },
                                                                ),
                                                              ),
                                                              Text(
                                                                  " प्राप्त रकम",
                                                                  style:
                                                                      labelStyle2)
                                                            ],
                                                          ),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          PaymentModeSelector(
                                                              onChangedFn: (v) {
                                                                if (v != 1 &&
                                                                    v != 2) {
                                                                  print(
                                                                      "objectid");
                                                                  print(v);
                                                                }
                                                                purchaseReturnController
                                                                    .transaction
                                                                    .value
                                                                    .txnPaymentTypeId = v;

                                                                purchaseReturnController
                                                                    .transaction
                                                                    .value
                                                                    .txnPaymentReference = null;
                                                                purchaseReturnController
                                                                    .transaction
                                                                    .value
                                                                    .chequeIssueDateBS = null;
                                                                purchaseReturnController
                                                                    .transaction
                                                                    .refresh();
                                                              },
                                                              paymentModeID:
                                                                  purchaseReturnController
                                                                      .transaction
                                                                      .value
                                                                      .txnPaymentTypeId,
                                                              enableFlag: !(purchaseReturnController
                                                                      .readOnlyFlag ||
                                                                  (!hasSubTotal)))
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 10,
                                                    ),
                                                    Expanded(
                                                      child:
                                                          FormBuilderTextField(
                                                        name: "txn_cash_amount",
                                                        readOnly: (purchaseReturnController
                                                                .readOnlyFlag ||
                                                            !hasSubTotal ||
                                                            iscashPurchaseReturn),
                                                        autocorrect: false,
                                                        keyboardType: TextInputType
                                                            .numberWithOptions(
                                                                decimal: true),
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        inputFormatters: [
                                                          FilteringTextInputFormatter
                                                              .allow(RegExp(
                                                                  r'^(\d+)?\.?\d{0,2}'))
                                                        ],
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Received Amount"),
                                                        textAlign:
                                                            TextAlign.end,
                                                        controller:
                                                            purchaseReturnController
                                                                .receivedAmountCtrl,
                                                        onChanged: (value) {
                                                          purchaseReturnController
                                                                  .receivedAmountCtrl
                                                                  .selection =
                                                              TextSelection.fromPosition(
                                                                  TextPosition(
                                                                      offset: purchaseReturnController
                                                                          .receivedAmountCtrl
                                                                          .text
                                                                          .length));
                                                          purchaseReturnController
                                                              .changeReceivedAmount(
                                                                  value ??
                                                                      "0.00",
                                                                  editorTag:
                                                                      'txn_cash_amount');
                                                        },
                                                      ),
                                                    )
                                                  ]),

                                              ...(purchaseReturnController
                                                          .transaction
                                                          .value
                                                          .txnPaymentTypeId ==
                                                      PAYMENT_MODE_CHEQUE_ID)
                                                  ? [
                                                      SizedBox(
                                                        height: 25,
                                                      ),
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text("चेक/भौचर न.",
                                                              style:
                                                                  labelStyle2),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          TextField(
                                                              autocorrect:
                                                                  false,
                                                              readOnly:
                                                                  purchaseReturnController
                                                                      .readOnlyFlag,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "Cheque/Voucher No."),
                                                              controller:
                                                                  purchaseReturnController
                                                                      .paymentRefCtrl,
                                                              onChanged: (v) {
                                                                purchaseReturnController
                                                                    .transaction
                                                                    .value
                                                                    .txnPaymentReference = v;
                                                                purchaseReturnController
                                                                    .transaction
                                                                    .refresh();
                                                              }),
                                                        ],
                                                      ),
                                                    ]
                                                  : [],
                                              if (purchaseReturnController
                                                      .transaction
                                                      .value
                                                      .txnPaymentTypeId ==
                                                  PAYMENT_MODE_CHEQUE_ID) ...[
                                                SizedBox(
                                                  height: 25,
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text("चेक मिति",
                                                        style: labelStyle2),
                                                    SizedBox(
                                                      height: 10,
                                                    ),
                                                    CustomDatePickerTextField(
                                                      labelText: "Cheque Date",
                                                      readOnly:
                                                          purchaseReturnController
                                                              .readOnlyFlag,
                                                      // maxBSDate: NepaliDateTime.now(),
                                                      initialValue:
                                                          purchaseReturnController
                                                              .transaction
                                                              .value
                                                              .chequeIssueDateBS,
                                                      onChange: (selectedDate) {
                                                        purchaseReturnController
                                                                .transaction
                                                                .value
                                                                .chequeIssueDateBS =
                                                            selectedDate;
                                                      },
                                                    ),
                                                  ],
                                                )
                                              ],

                                              SizedBox(
                                                height: 25,
                                              ),

                                              // =============================================Balance Amount
                                              ...iscashPurchaseReturn
                                                  ? []
                                                  : [
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                              "बाँकी रहेको रकम",
                                                              style:
                                                                  labelStyle2),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          FormBuilderTextField(
                                                            name:
                                                                "txn_balance_amount",
                                                            readOnly: true,
                                                            autocorrect: false,
                                                            keyboardType:
                                                                TextInputType
                                                                    .numberWithOptions(
                                                                        decimal:
                                                                            true),
                                                            textInputAction:
                                                                TextInputAction
                                                                    .done,
                                                            style:
                                                                formFieldTextStyle,
                                                            decoration: formFieldStyle
                                                                .copyWith(
                                                                    labelText:
                                                                        "Balance Due"),
                                                            textAlign:
                                                                TextAlign.end,
                                                            controller:
                                                                purchaseReturnController
                                                                    .dueAmountCtrl,
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(
                                                        height: 20,
                                                      ),
                                                    ],
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  //===============================================Image
                                  Container(
                                    width: double.infinity,
                                    child: Card(
                                      elevation: 2,
                                      child: Container(
                                        child: Container(
                                            // color: Colors.red,
                                            height: 140,
                                            width: 100,
                                            // width: ,
                                            // child: (null==state.selectImage)?
                                            child: FormBuilderImagePicker(
                                              name: "image_picker",
                                              bottomSheetPadding:
                                                  EdgeInsets.all(0),

                                              // maxHeight: 100,
                                              // maxWidth: 100,

                                              decoration: InputDecoration(
                                                border: InputBorder.none,
                                              ),
                                              maxImages: 2,
                                              iconColor: colorPrimaryLight,
                                              onChanged: (_fls) async {
                                                if (_fls != null &&
                                                    _fls.isNotEmpty) {
                                                  _fls.forEach((element) {
                                                    purchaseReturnController
                                                        .files
                                                        .add(
                                                            File(element.path));
                                                  });
                                                }

                                                purchaseReturnController.files
                                                    .refresh();

                                                bool isLargeFile =
                                                    await purchaseReturnController
                                                        .checkLargeImage(_fls!);
                                                if (isLargeFile) {
                                                  showToastMessage(context,
                                                      message:
                                                          MAX_IMAGE_SIZE_MESSAGE,
                                                      alertType:
                                                          AlertType.Error);
                                                  return;
                                                }
                                              },
                                            )

                                            //     :customImageBox(
                                            //     state.selectImage,
                                            //     onCancel: (purchaseReturnController.readOnlyFlag)?  null : () => state.imageCancelButtonOnClickHandler()
                                            // ),
                                            ),
                                      ),
                                    ),
                                  ),

                                  //===============================================Description
                                  Card(
                                    elevation: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 10),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "कैफियत",
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                            name: "description",
                                            readOnly: purchaseReturnController
                                                .readOnlyFlag,
                                            autocorrect: false,
                                            textAlign: TextAlign.start,
                                            textInputAction:
                                                TextInputAction.newline,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText: "Remarks"),
                                            minLines: 4,
                                            maxLines: 4,
                                            controller: purchaseReturnController
                                                .descCtrl,
                                            onChanged: (value) {
                                              purchaseReturnController
                                                  .transaction
                                                  .value
                                                  .txnDescription = value;
                                              purchaseReturnController
                                                  .transaction
                                                  .refresh();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                //=================================================Save button
                bottomNavigationBar: BottomSaveCancelButton(
                  shadow: false,
                  // hasDelete: (purchaseReturnController.editFlag &&
                  //         !purchaseReturnController.readOnlyFlag)
                  //     ? true
                  //     : false,
                  // onDeleteBtnPressedFn: () async {
                  //   showAlertDialog(context,
                  //       okText: "YES",
                  //       alertType: AlertType.Error,
                  //       alertTitle: "Confirm Delete",
                  //       onCloseButtonPressed: (_) async {
                  //     Navigator.of(_).pop();
                  //     ProgressDialog progressDialog = ProgressDialog(context,
                  //         type: ProgressDialogType.Normal, isDismissible: false);
                  //     progressDialog.update(
                  //         message: "Checking Permission. Please wait....");
                  //     await progressDialog.show();
                  //     Tuple2<bool, String> checkResp =
                  //         await PermissionWrapperController()
                  //             .requestForPermissionCheck(
                  //                 forPermission:
                  //                     PermissionManager.purchaseReturnDelete);
                  //     if (checkResp.item1) {
                  //       //has  permission
                  //       progressDialog.update(
                  //           message: "Deleting Data. Please wait....");
                  //       Tuple2<bool, String> deleteResp =
                  //           await TransactionRepository()
                  //               .delete(this.purchaseReturnID);
                  //       await progressDialog.hide();
                  //       if (deleteResp.item1) {
                  //         //  data deleted
                  //         TransactionHelper.refreshPreviousPages();
                  //         showAlertDialog(context,
                  //             barrierDismissible: false,
                  //             alertType: AlertType.Success,
                  //             alertTitle: "", onCloseButtonPressed: (_) {
                  //           Navigator.of(_).pop();
                  //           Navigator.of(context).pop();
                  //         }, message: deleteResp.item2);
                  //       } else {
                  //         //cannot  delete  data
                  //         showAlertDialog(context,
                  //             alertType: AlertType.Error,
                  //             alertTitle: "",
                  //             message: deleteResp.item2);
                  //       }
                  //     } else {
                  //       await progressDialog.hide();
                  //       showAlertDialog(context,
                  //           alertType: AlertType.Error,
                  //           alertTitle: "",
                  //           message: checkResp.item2);
                  //     }
                  //   },
                  //       message:
                  //           "Are you sure you  want to  delete this purchase return record?");
                  // },
                  hasNew: !purchaseReturnController.editFlag,
                  onSaveAndNewBtnPressedFn: () {
                    this.onSave(context, forNew: true);
                  },
                  enableFlag: !purchaseReturnController.readOnlyFlag,
                  onSaveBtnPressedFn: (purchaseReturnController.readOnlyFlag)
                      ? null
                      : () async {
                          this.onSave(context);
                        },
                )));
      }
    });
  }
}

Widget getItemListView(
  BuildContext context,
  List<LineItemDetailModel> _items,
  AddEditPurchaseReturnController purchaseReturnController,
) {
  // return Container(height: 40, color: Colors.red);
  ScrollController scrollController = ScrollController();
  var listView = Scrollbar(
    controller: scrollController,
    thumbVisibility: true,
    child: ListView.builder(
        controller: scrollController,
        itemCount: _items.length,
        shrinkWrap: true,
        itemBuilder: (context, int index) {
          LineItemDetailModel _item = _items[index];

          return Row(children: [
            Expanded(
                child: GestureDetector(
              onTap: purchaseReturnController.readOnlyFlag
                  ? null
                  : () async {
                      var returnedData = await showDialog(
                          context: context,
                          useRootNavigator: true,
                          barrierDismissible: false,
                          builder: (d_c) {
                            return AlertDialog(
                                insetPadding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                contentPadding: EdgeInsets.zero,
                                clipBehavior: Clip.hardEdge,
                                content: Container(
                                  width: MediaQuery.of(context).size.width - 20,
                                  child:
                                      AddEditPurchaseReturnBilledItemScreenView(
                                    lineItemModel: _item,
                                  ),
                                ));
                          });
                      if (null != returnedData) {
                        if (returnedData.deleteFlag) {
                          purchaseReturnController.items.removeAt(index);
                        } else if (null != returnedData.billedItem) {
                          purchaseReturnController.items.replaceRange(
                              index, 1, [returnedData.billedItem]);
                          purchaseReturnController.recalculateForItems();
                        }
                        purchaseReturnController.items.refresh();
                        purchaseReturnController.recalculateForItems();
                      }
                    },
              child: Card(
                elevation: 2,
                margin: EdgeInsets.only(
                    left: 10,
                    right: 10,
                    top: (0 == index) ? 15 : 8,
                    bottom: ((_items.length - 1) == index) ? 20 : 8),
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                  decoration: BoxDecoration(color: Colors.black12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // =============================================item name
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              _item.itemName ?? "",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimaryDark),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Container(
                            child: GestureDetector(
                              onTap: purchaseReturnController.readOnlyFlag
                                  ? null
                                  : () {
                                      purchaseReturnController.items
                                          .removeAt(index);
                                      purchaseReturnController.items.refresh();
                                      purchaseReturnController
                                          .recalculateForItems();
                                    },
                              child: Icon(Icons.delete,
                                  color: (purchaseReturnController.readOnlyFlag)
                                      ? Colors.black54
                                      : Colors.red),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),

                      // =============================================gross amount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: 75,
                            child: Text(
                              "Amount",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              "${_item.quantity} ${_item.lineItemUnitName ?? ""} X ${formatCurrencyAmount(_item.pricePerUnit ?? 0.00)} = ${formatCurrencyAmount(_item.grossAmount ?? 0.00, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),

                      // =============================================Discount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: 100,
                            child: Text(
                              "Discount(%): ${_item.discountPercent}",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              " = ${formatCurrencyAmount(_item.discountAmount ?? 0.00, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      Divider(
                        height: 5,
                      ),
                      Divider(
                        height: 0,
                      ),

                      // =============================================netAmount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              formatCurrencyAmount(
                                  _item.totalAmount ?? 0.00, false),
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimary),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )),
          ]);
        }),
  );

  return listView;
}
